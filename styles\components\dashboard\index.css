/* Dashboard Components CSS - Main Import File */

/* Import all dashboard component styles */
@import './stats-card.css';
@import './header.css';
@import './chart-card.css';
@import './progress-card.css';
@import './ocean-card.css';
@import './viais-card.css';
@import './world-map-card.css';
@import './assessment-table.css';

/* Dashboard Global Variables */
:root {
  /* Color Palette */
  --dashboard-text-primary: #1e1e1e;
  --dashboard-text-secondary: #64707d;
  --dashboard-text-light: #f1f1f1;
  --dashboard-primary-blue: #6475e9;
  --dashboard-primary-blue-hover: #5a6bd8;
  --dashboard-light-blue: #a2acf2;
  --dashboard-border: #eaecf0;
  --dashboard-background-white: #ffffff;
  --dashboard-background-light: #f3f3f3;
  --dashboard-background-card: #f8f9fa;
  --dashboard-background-indicator: #e5e7eb;
  
  /* Status Colors */
  --dashboard-success-bg: #dcfce7;
  --dashboard-success-text: #166534;
  --dashboard-success-border: #bbf7d0;
  --dashboard-warning-bg: #fef3c7;
  --dashboard-warning-text: #92400e;
  --dashboard-warning-border: #fde68a;
  --dashboard-danger-text: #dc2626;
  --dashboard-danger-bg: #fef2f2;
  --dashboard-danger-hover: #b91c1c;
  
  /* Spacing */
  --dashboard-spacing-xs: 0.25rem;
  --dashboard-spacing-sm: 0.5rem;
  --dashboard-spacing-md: 1rem;
  --dashboard-spacing-lg: 1.5rem;
  --dashboard-spacing-xl: 2rem;
  
  /* Border Radius */
  --dashboard-radius-sm: 0.375rem;
  --dashboard-radius-md: 0.5rem;
  --dashboard-radius-lg: 0.75rem;
  --dashboard-radius-xl: 1rem;
  --dashboard-radius-2xl: 1.5rem;
  
  /* Shadows */
  --dashboard-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --dashboard-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  
  /* Typography */
  --dashboard-font-size-xs: 0.75rem;
  --dashboard-font-size-sm: 0.875rem;
  --dashboard-font-size-base: 1rem;
  --dashboard-font-size-lg: 1.125rem;
  --dashboard-font-size-xl: 1.25rem;
  --dashboard-font-size-2xl: 1.5rem;
  --dashboard-font-size-3xl: 1.875rem;
  
  /* Chart Dimensions */
  --dashboard-chart-height: 128px;
  --dashboard-chart-bar-min-height: 20px;
  --dashboard-chart-bar-width: 2rem;
  
  /* Icon Sizes */
  --dashboard-icon-sm: 1rem;
  --dashboard-icon-md: 1.5rem;
  --dashboard-icon-lg: 2rem;
  
  /* Avatar Sizes */
  --dashboard-avatar-sm: 2rem;
  --dashboard-avatar-md: 2.5rem;
  --dashboard-avatar-lg: 4rem;
}

/* Dashboard Layout Utilities */
.dashboard-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.dashboard-grid {
  @apply grid gap-6;
}

.dashboard-grid--2-cols {
  @apply grid-cols-1 lg:grid-cols-2;
}

.dashboard-grid--3-cols {
  @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
}

.dashboard-grid--4-cols {
  @apply grid-cols-1 sm:grid-cols-2 lg:grid-cols-4;
}

/* Common Dashboard Component Patterns */
.dashboard-card {
  @apply bg-white rounded-lg border shadow-sm;
  border-color: var(--dashboard-border);
}

.dashboard-card--elevated {
  @apply shadow-md;
}

.dashboard-text--primary {
  color: var(--dashboard-text-primary);
}

.dashboard-text--secondary {
  color: var(--dashboard-text-secondary);
}

.dashboard-text--light {
  color: var(--dashboard-text-light);
}

.dashboard-bg--primary {
  background-color: var(--dashboard-primary-blue);
}

.dashboard-bg--primary:hover {
  background-color: var(--dashboard-primary-blue-hover);
}

/* Responsive Design Helpers */
@media (max-width: 768px) {
  .dashboard-grid {
    @apply gap-4;
  }
  
  .dashboard-container {
    @apply px-2;
  }
}

/* Animation Utilities */
.dashboard-transition {
  @apply transition-all duration-300 ease-in-out;
}

.dashboard-fade-in {
  @apply animate-in fade-in duration-500;
}

/* Focus States */
.dashboard-focus {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  --tw-ring-color: var(--dashboard-primary-blue);
}
